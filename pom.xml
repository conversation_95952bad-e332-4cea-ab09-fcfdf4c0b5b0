<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.krogerqa</groupId>
    <artifactId>sfp-karate-framework</artifactId>
    <version>1.0-SNAPSHOT</version>
    <dependencies>
        <dependency>
            <groupId>com.krogerqa.karate</groupId>
            <artifactId>kafka</artifactId>
            <version>2024.2.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.krogerqa.karate</groupId>
            <artifactId>karate-central-framework</artifactId>
            <version>2025.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ibm.mq</groupId>
                    <artifactId>com.ibm.mq.allclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.datastax.cassandra</groupId>
                    <artifactId>cassandra-driver-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.skyscreamer</groupId>
                    <artifactId>jsonassert</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.epam.reportportal</groupId>
                    <artifactId>client-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.epam.reportportal</groupId>
                    <artifactId>logger-java-logback</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.masterthought</groupId>
                    <artifactId>cucumber-reporting</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.krogerqa.qmetry</groupId>
            <artifactId>qmetry-import-utility</artifactId>
            <version>2025.1.1</version>
            <classifier>jar-with-dependencies</classifier>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>net.masterthought</groupId>
            <artifactId>cucumber-reporting</artifactId>
            <version>5.7.7</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <testResources>
            <testResource>
                <directory>src/test/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
            <testResource>
                <directory>src/test/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>11</source>
                    <target>11</target>
                    <compilerArgument>-Werror</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>1.2.1</version>
                <configuration>
                    <mainClass>com.kroger.qmetry.ImportResultsQmetry</mainClass>
                    <arguments>
                        <argument>target/karate-reports</argument>
                        <!--suppress UnresolvedMavenProperty -->
                        <argument>${suiteName}</argument>
                    </arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.2</version>
                 <configuration>
                    <systemPropertyVariables>
                        <rp.enable>${reportportal}</rp.enable>
                    </systemPropertyVariables>
                    <systemPropertiesFile>src/test/resources/reportportal.properties</systemPropertiesFile>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>